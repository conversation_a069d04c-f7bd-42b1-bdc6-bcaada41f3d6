// Simple test script to verify email OTP functionality
const dotenv = require('dotenv');
dotenv.config();

const { sendOTP } = require('./utils/sendOTP');

// Test user object
const testUser = {
  email: '<EMAIL>',
  firstName: 'Test',
  lastName: 'User'
};

const testOTP = '123456';

console.log('Testing email OTP functionality...');
console.log('Test user:', testUser);
console.log('Test OTP:', testOTP);

// Test the sendOTP function
sendOTP(testUser, testOTP)
  .then(result => {
    console.log('✅ SUCCESS: OTP sent successfully');
    console.log('Result:', result);
    process.exit(0);
  })
  .catch(error => {
    console.error('❌ ERROR: Failed to send OTP');
    console.error('Error:', error.message);
    process.exit(1);
  });
